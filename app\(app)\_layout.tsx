import { Tabs } from 'expo-router';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { Platform } from 'react-native';

import usePushNotifications from '../../hooks/usePushNotifications';
import { colors } from '../../constants/Colors'; // Assumed to potentially have 'textSecondary' now

// Define a precise type for Feather icon names for reusability and clarity.
type FeatherIconName = React.ComponentProps<typeof Feather>['name'];

// Define the set of valid tab names. This can be leveraged by Expo Router's typed routes if configured.
// This ensures that only valid route names are used in the configuration.
type TabRouteName = 'home' | 'search' | 'calendar' | 'settings';

// Define the structure for individual tab screen configurations.
// This enhances type safety and code readability.
interface TabScreenConfig {
  readonly name: TabRouteName;
  readonly title: string;
  readonly iconName: FeatherIconName;
  readonly accessibilityLabel: string;
  readonly headerShown?: boolean; // Optional: if not set, global screenOptions.headerShown applies.
}

const tabScreenConfigs: readonly TabScreenConfig[] = [
  {
    name: 'home',
    title: 'Home',
    iconName: 'gift',
    accessibilityLabel: 'Home Tab, Gift Recommendations',
    headerShown: false,
  },
  {
    name: 'search',
    title: 'Search',
    iconName: 'search',
    accessibilityLabel: 'Search Tab, Find Gift Ideas',
    headerShown: false,
  },
  {
    name: 'calendar',
    title: 'Calendar',
    iconName: 'calendar',
    accessibilityLabel: 'Calendar Tab, Important Dates',
    headerShown: false,
  },
  {
    name: 'settings',
    title: 'Settings',
    iconName: 'settings',
    accessibilityLabel: 'Settings Tab, App Configuration',
    headerShown: false, // Explicitly hide header for settings, overriding the global default if it were true.
  },
];

interface TabBarIconProps {
  name: FeatherIconName;
  color: string;
  size: number;
}

// A dedicated component for tab icons enhances clarity and reusability.
const TabBarIcon: React.FC<TabBarIconProps> = ({ name, color, size }) => {
  return <Feather name={name} size={size} color={color} />;
};

export default function AppLayout() {
  usePushNotifications(); // Initialize push notifications
  const { colorScheme } = useColorScheme(); // For dark mode awareness

  // Define theme-aware colors based on the Giftmi Style Guide
  const activeTintColor =
    colorScheme === 'dark' ? colors.primary.dark : colors.primary.DEFAULT;
  // Set tab bar background to match page background
  const tabBarBackgroundColor =
    colorScheme === 'dark' ? colors.background.dark : colors.background.DEFAULT;
  const tabBarBorderColor =
    colorScheme === 'dark' ? colors.border.dark : colors.border.DEFAULT;

  return (
    <Tabs
      screenOptions={{
        headerShown: false, // Global Default: hide headers for all tab screens.
        // Individual screens can override this in their `options`.
        tabBarActiveTintColor: activeTintColor,
        tabBarStyle: {
          backgroundColor: tabBarBackgroundColor,
          borderTopWidth:0,
          borderTopColor: tabBarBorderColor,
          ...(Platform.OS === 'ios'
            ? {
                // iOS shadow color now uses the theme-aware tabBarBorderColor
                shadowColor: tabBarBorderColor,
                shadowOffset: { width: 0, height: -0.5 },
                shadowOpacity: 0.1,
                shadowRadius: 2, // Adjusted for a subtle blur (LOW 1)
              }
            : {
                // Android: elevation: 0 is maintained for a flat design,
                // can be adjusted if a shadow is desired.
                elevation: 0,
              }),
        },
        tabBarShowLabel: true,
        tabBarLabelStyle: {
          // TODO: Define font family and size once typography scale is finalized in the style guide.
          // Tracked: Ensure this is addressed per project management.
        },
      }}
    >
      {tabScreenConfigs.map((tab) => (
        <Tabs.Screen
          key={tab.name}
          name={tab.name}
          options={{
            title: tab.title,
            tabBarIcon: (
              { color, size } // Removed focused and image logic
            ) => {
              const iconSize = size ; // Increase size slightly
              return (
                <TabBarIcon
                  name={tab.iconName}
                  color={color}
                  size={iconSize}
                />
              );
            },
            tabBarAccessibilityLabel: tab.accessibilityLabel,
            headerShown: tab.headerShown,
          }}
        />
      ))}

      {/* Screens that should NOT be tabs, hidden from the tab bar */}
      <Tabs.Screen name="profiles" options={{ href: null }} />
      <Tabs.Screen name="onboarding" options={{ href: null }} />
      {/* Add other non-tab screens here if necessary, e.g.: */}
      {/* <Tabs.Screen name="gifts/details" options={{ href: null }} /> */}
      {/* <Tabs.Screen name="gifts/add" options={{ href: null }} /> */}
    </Tabs>
  );
}
