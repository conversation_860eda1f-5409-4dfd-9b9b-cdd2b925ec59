import React, { useEffect } from 'react';
import { View, Text, SafeAreaView, ScrollView, Image } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { useQuestionnaire } from '@/contexts/QuestionnaireContext';
import Animated, { FadeInDown, FadeIn, ZoomIn } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import Button from '@/components/ui/Button';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';

// Gift recommendation database based on interests and preferences
const GIFT_DATABASE = {
  technology: [
    { title: "Wireless Earbuds", category: "Technology", price: "$89", confidence: "95%" },
    { title: "Smart Watch", category: "Technology", price: "$199", confidence: "92%" },
    { title: "Portable Charger", category: "Technology", price: "$35", confidence: "88%" },
  ],
  fashion: [
    { title: "Silk Scarf", category: "Fashion", price: "$65", confidence: "90%" },
    { title: "Leather Wallet", category: "Fashion", price: "$85", confidence: "87%" },
    { title: "Designer Sunglasses", category: "Fashion", price: "$120", confidence: "93%" },
  ],
  cooking: [
    { title: "Artisan Coffee Set", category: "Food & Drink", price: "$67", confidence: "92%" },
    { title: "Premium Spice Collection", category: "Food & Drink", price: "$45", confidence: "89%" },
    { title: "Cast Iron Skillet", category: "Kitchen", price: "$75", confidence: "91%" },
  ],
  reading: [
    { title: "Cozy Reading Blanket", category: "Home & Comfort", price: "$45", confidence: "88%" },
    { title: "Book Light", category: "Reading", price: "$25", confidence: "85%" },
    { title: "Leather Journal", category: "Stationery", price: "$35", confidence: "87%" },
  ],
  wellness: [
    { title: "Essential Oil Diffuser", category: "Wellness", price: "$55", confidence: "90%" },
    { title: "Yoga Mat", category: "Fitness", price: "$65", confidence: "88%" },
    { title: "Meditation Cushion", category: "Wellness", price: "$40", confidence: "86%" },
  ],
  travel: [
    { title: "Travel Organizer Set", category: "Travel", price: "$45", confidence: "89%" },
    { title: "Portable Luggage Scale", category: "Travel", price: "$20", confidence: "85%" },
    { title: "Travel Journal", category: "Travel", price: "$30", confidence: "87%" },
  ],
  // Default fallback gifts
  default: [
    { title: "Scented Candle Set", category: "Home & Comfort", price: "$35", confidence: "85%" },
    { title: "Gourmet Chocolate Box", category: "Food & Drink", price: "$25", confidence: "82%" },
    { title: "Plant Care Kit", category: "Home & Garden", price: "$40", confidence: "84%" },
  ]
};

// Function to generate personalized insights
const generatePersonalizedInsight = (answers: Record<number, any>) => {
  const name = answers[1]?.answer || 'They';
  const interests = answers[3]?.answer || [];
  const relationship = answers[2]?.answer || 'friend';
  const giftType = answers[7]?.answer || 'practical';
  const styleVibe = answers[4]?.answer || '';

  let insight = `Based on what you've told us about ${name}, `;

  if (Array.isArray(interests) && interests.length > 0) {
    const interestList = interests.slice(0, 2).join(' and ');
    insight += `their love for ${interestList} suggests they appreciate `;
  } else {
    insight += `they seem to appreciate `;
  }

  switch (giftType) {
    case 'practical':
      insight += `useful items that enhance their daily routine. `;
      break;
    case 'experiential':
      insight += `memorable experiences over material things. `;
      break;
    case 'sentimental':
      insight += `meaningful, personal touches that show you care. `;
      break;
    case 'fun':
      insight += `unique surprises that bring joy and excitement. `;
      break;
    default:
      insight += `thoughtful gifts that match their personality. `;
  }

  if (styleVibe) {
    insight += `Their ${styleVibe} style suggests they'd love items that reflect this aesthetic.`;
  } else {
    insight += `Consider their personal style when making your final choice.`;
  }

  return insight;
};

// Function to generate personalized gift recommendations
const generateGiftRecommendations = (answers: Record<number, any>) => {
  const interests = answers[3]?.answer || []; // Question 3: interests
  const relationship = answers[2]?.answer || 'friend'; // Question 2: relationship
  const giftType = answers[7]?.answer || 'practical'; // Question 7: gift type preference

  let recommendations = [];

  // Get gifts based on interests
  if (Array.isArray(interests) && interests.length > 0) {
    interests.forEach(interest => {
      const giftCategory = GIFT_DATABASE[interest as keyof typeof GIFT_DATABASE];
      if (giftCategory) {
        recommendations.push(...giftCategory.slice(0, 1)); // Take 1 gift per interest
      }
    });
  }

  // If we don't have enough recommendations, add from default
  while (recommendations.length < 3) {
    const remaining = 3 - recommendations.length;
    recommendations.push(...GIFT_DATABASE.default.slice(0, remaining));
  }

  // Add IDs and adjust confidence based on relationship
  return recommendations.slice(0, 3).map((gift, index) => ({
    ...gift,
    id: index + 1,
    confidence: relationship === 'partner' ?
      `${Math.min(95, parseInt(gift.confidence) + 5)}%` :
      gift.confidence
  }));
};

export default function QuestionnaireComplete() {
  const router = useRouter();
  const { user } = useAuth();
  const { answers, getQuestionnaireData, completeQuestionnaire } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Generate personalized gift recommendations and insights
  const giftRecommendations = generateGiftRecommendations(answers);
  const personalizedInsight = generatePersonalizedInsight(answers);
  const questionnaireData = getQuestionnaireData();

  // Redirect if user is not authenticated
  useEffect(() => {
    if (!user) {
      router.replace('/(auth)/login');
    }
  }, [user, router]);

  // Celebration haptic on mount
  useEffect(() => {
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
  }, []);

  const handleCreateProfile = async () => {
    try {
      // Mark questionnaire as completed in context
      await completeQuestionnaire();

      // Navigate to profile creation with questionnaire data
      router.push('/profiles/add?fromQuestionnaire=true');
    } catch (error) {
      console.error('Error completing questionnaire:', error);
      // Still navigate even if completion fails
      router.push('/profiles/add?fromQuestionnaire=true');
    }
  };

  const handleViewRecommendations = () => {
    // For now, just go to home - later this will show recommendations
    router.push('/(app)/home');
  };

  if (!user) {
    return null;
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <ScrollView className="flex-1 px-6">
        {/* Success Header */}
        <Animated.View 
          entering={FadeIn.duration(800)}
          className="items-center mt-12 mb-8"
        >
          <Animated.View 
            entering={ZoomIn.duration(600).delay(200)}
            className="p-6 mb-6 bg-green-100 dark:bg-green-900/30 rounded-full"
          >
            <Feather name="check-circle" size={64} color="#10B981" />
          </Animated.View>
          
          <Animated.Text 
            entering={FadeInDown.duration(600).delay(400)}
            className="text-3xl font-bold text-center text-text-primary dark:text-text-primary-dark mb-3"
          >
            Perfect! 🎉
          </Animated.Text>
          
          <Animated.Text 
            entering={FadeInDown.duration(600).delay(500)}
            className="text-lg text-center text-text-secondary dark:text-text-secondary-dark leading-6"
          >
            We've analyzed your answers and found some amazing gift ideas!
          </Animated.Text>
        </Animated.View>

        {/* Gift Preview Section */}
        <Animated.View 
          entering={FadeInDown.duration(600).delay(600)}
          className="mb-8"
        >
          <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark mb-4">
            Top Gift Recommendations
          </Text>
          
          <View className="gap-3">
            {giftRecommendations.map((gift, index) => (
              <Animated.View
                key={gift.id}
                entering={FadeInDown.duration(400).delay(700 + index * 100)}
                className="p-4 bg-card dark:bg-card-dark rounded-xl border border-border dark:border-border-dark"
              >
                <View className="flex-row items-center">
                  {/* Placeholder Image */}
                  <View className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mr-4 items-center justify-center">
                    <Feather name="gift" size={24} color="#A3002B" />
                  </View>
                  
                  <View className="flex-1">
                    <Text className="text-lg font-semibold text-text-primary dark:text-text-primary-dark">
                      {gift.title}
                    </Text>
                    <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
                      {gift.category}
                    </Text>
                    <View className="flex-row items-center mt-1">
                      <Text className="text-base font-bold text-primary mr-3">
                        {gift.price}
                      </Text>
                      <View className="flex-row items-center">
                        <Feather name="target" size={14} color="#10B981" />
                        <Text className="text-sm text-green-600 dark:text-green-400 ml-1">
                          {gift.confidence} match
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              </Animated.View>
            ))}
          </View>
        </Animated.View>

        {/* Insights */}
        <Animated.View 
          entering={FadeInDown.duration(600).delay(1000)}
          className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl mb-8"
        >
          <View className="flex-row items-center mb-2">
            <Feather name="lightbulb" size={20} color="#3B82F6" />
            <Text className="ml-2 text-base font-semibold text-blue-700 dark:text-blue-300">
              Gift Insight
            </Text>
          </View>
          <Text className="text-sm text-blue-600 dark:text-blue-400">
            {personalizedInsight}
          </Text>
        </Animated.View>

        {/* Pre-fill Information */}
        <Animated.View
          entering={FadeInDown.duration(600).delay(1100)}
          className="p-4 bg-green-50 dark:bg-green-900/20 rounded-xl mb-6"
        >
          <View className="flex-row items-center mb-2">
            <Feather name="zap" size={20} color="#10B981" />
            <Text className="ml-2 text-base font-semibold text-green-700 dark:text-green-300">
              Smart Pre-fill Ready!
            </Text>
          </View>
          <Text className="text-sm text-green-600 dark:text-green-400">
            We'll automatically fill in the profile form with your questionnaire answers to save you time. You can always edit or add more details later.
          </Text>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View
          entering={FadeInDown.duration(600).delay(1200)}
          className="gap-4 mb-8"
        >
          <Button
            title="Complete Profile Setup"
            onPress={handleCreateProfile}
            variant="primary"
            className="w-full"
            leftIcon={<Feather name="user-plus" size={20} color="white" />}
          />
          
          <Button
            title="Explore More Gifts"
            onPress={handleViewRecommendations}
            variant="secondary"
            className="w-full"
            leftIcon={<Feather name="search" size={20} color="#A3002B" />}
          />
        </Animated.View>

        {/* Progress Celebration */}
        <Animated.View 
          entering={FadeInDown.duration(600).delay(1400)}
          className="items-center mb-8"
        >
          <View className="flex-row items-center">
            <Feather name="star" size={16} color="#F59E0B" />
            <Text className="mx-2 text-sm text-text-secondary dark:text-text-secondary-dark">
              You're all set to discover amazing gifts!
            </Text>
            <Feather name="star" size={16} color="#F59E0B" />
          </View>
        </Animated.View>
      </ScrollView>
    </SafeAreaView>
  );
}
