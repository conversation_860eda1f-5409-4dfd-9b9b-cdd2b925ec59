# Post-Login Questionnaire Onboarding Plan

## Executive Summary
Transform the current "create profile" button approach into an engaging, addictive questionnaire-based onboarding flow that automatically redirects first-time users after login.

## Current Problems Identified
- **Jarring UX**: Users see "Create a Gift Profile" button on empty home screen
- **Overwhelming Form**: 8-section accordion form with high cognitive load
- **No Progressive Disclosure**: All sections visible at once
- **Missing Context**: No explanation of data value for gift recommendations
- **High Abandonment Risk**: Complex form structure deters completion

## Proposed Solution: Questionnaire-Based Onboarding

### Core Approach
- **Auto-redirect** first-time users (no profiles) to engaging questionnaire
- **8-10 conversational questions** with visual elements
- **Immediate value** through gift recommendation previews
- **Pre-fill profile form** with questionnaire responses
- **Optional detailed completion** - users can skip and return later

### Data Collection Strategy

#### Questionnaire Data (Essential for Immediate Value)
**Phase 1 - Core Identity (3-4 questions):**
- Name & relationship
- Primary interest category (visual selection)
- One key dislike/avoid
- Approximate age range

**Phase 2 - Gift Preferences (3-4 questions):**
- Budget comfort zone (ranges)
- Practical vs. experiential preference
- Style preference (visual mood boards)
- Special occasions they care about

**Phase 3 - Personality Insights (2-3 questions):**
- Social vs. solo activities
- Trendy vs. classic preference
- Surprise vs. practical gifts

#### Profile Form Data (Detailed Information)
- Detailed interests/hobbies (tag input)
- Specific dislikes (tag input)
- Exact sizes (clothing, shoes)
- Specific brand preferences
- Wishlist items, past gifts, custom dates, notes

### Engagement Techniques

#### Gamification
- Animated progress bars with milestones
- Achievement badges for completion
- "Unlocking" sections progressively
- Personality-driven question copy

#### Visual & Interactive Elements
- Image grids for style preferences
- Color palette selections
- Mood boards for aesthetics
- Story-driven background changes
- Satisfying micro-animations
- Haptic feedback

#### Psychological Hooks
- Curiosity gaps: "Building [Name]'s gift DNA..."
- Social validation: "Most people say..."
- Anticipation building: Preview recommendations
- Variable reward schedule: Surprise insights

### Technical Implementation

#### Architecture
```
app/(app)/onboarding/
├── questionnaire/
│   ├── index.tsx (entry point)
│   ├── [step].tsx (dynamic step routing)
│   └── complete.tsx (completion screen)
```

#### State Management
- `QuestionnaireContext` for response management
- Integration with existing `AuthContext`
- AsyncStorage for session persistence

#### Data Flow
1. Questionnaire → Temporary Storage
2. Pre-fill Profile Form with responses
3. Smart defaults based on questionnaire
4. Progressive enhancement for detailed data

### Implementation Phases

**Phase 1: Foundation (Week 1)**
- Questionnaire routing structure
- Basic question components
- State management implementation

**Phase 2: Engagement (Week 2)**
- Visual elements and animations
- Progress tracking
- Completion flow

**Phase 3: Integration (Week 3)**
- Profile creation system connection
- Data pre-filling implementation
- User flow routing logic

**Phase 4: Polish (Week 4)**
- Micro-interactions and animations
- Skip functionality
- Testing and refinement

### Success Metrics
- Questionnaire completion rate
- Profile creation completion rate
- Time to first gift recommendation
- User engagement with recommendations
- Retention after onboarding

### Risk Mitigation
- Graceful fallbacks if questionnaire fails
- Skip option for users who prefer direct form
- Data validation for profile schema mapping
- Performance optimization for smooth experience

## Finalized Requirements

### User Experience
- **Duration**: Quick questionnaire (2-3 minutes max)
- **Gift Previews**: Static recommendations based on choices
- **Skip Option**: Available, redirects to profile creation page
- **Progress Saving**: Auto-save progress for resume capability

### Technical Specifications
- **User Detection**: Check if `profiles.length === 0`
- **Flow Integration**: Onboarding → Signup/Login → Questionnaire → Profile Creation
- **Analytics**: Not required initially (no user base yet)

### Visual & UX Guidelines
- **Visual Assets**: Brand new elements, use `assets/images/pastgifts.png` as placeholder
- **Tone**: Matches app theme (gift-focused, thoughtful, engaging)
- **Platform**: React Native best practices, mobile-first design

### Data Pre-filling Implementation
```typescript
interface QuestionnaireData {
  name: string;
  relationship: string;
  primaryInterests: string[];
  keyDislikes: string[];
  budgetRange: { min: number; max: number };
  stylePreference: string;
  favoriteColor: string;
  giftType: 'practical' | 'experiential' | 'mixed';
}

const mapQuestionnaireToProfileForm = (questionnaire: QuestionnaireData): Partial<ProfileFormData> => {
  return {
    name: questionnaire.name,
    relationship: questionnaire.relationship,
    interestsInput: questionnaire.primaryInterests.join(', '),
    dislikesInput: questionnaire.keyDislikes.join(', '),
    preferences: {
      favoriteColor: questionnaire.favoriteColor,
      preferredStyle: questionnaire.stylePreference,
      budgetMin: questionnaire.budgetRange.min,
      budgetMax: questionnaire.budgetRange.max,
      favoriteBrands: [],
    },
    // Other fields remain empty for detailed completion
  };
};
```

## Implementation Plan - APPROVED FOR DEVELOPMENT

### Phase 1: Foundation (Days 1-2)
1. Create questionnaire routing structure
2. Build QuestionnaireContext for state management
3. Implement progress saving with AsyncStorage
4. Create basic question components

### Phase 2: Core Questions (Days 3-4)
1. Design and implement 6-8 core questions
2. Add visual selection components
3. Implement skip functionality
4. Create static gift preview system

### Phase 3: Integration (Days 5-6)
1. Modify app routing for first-time user detection
2. Implement data pre-filling for profile form
3. Connect questionnaire completion to profile creation
4. Add smooth transitions and animations

### Phase 4: Polish (Days 7-8)
1. Add micro-interactions and haptic feedback
2. Implement progress indicators and celebrations
3. Testing and refinement
4. Performance optimization

## Ready for Implementation ✅
