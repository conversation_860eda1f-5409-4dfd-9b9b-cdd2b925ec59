import React, { useEffect } from 'react';
import { View, Text, SafeAreaView } from 'react-native';
import { useRouter } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import Animated, { FadeInDown, FadeIn } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import Button from '@/components/ui/Button';
import { useColorScheme } from 'nativewind';
import { TOTAL_QUESTIONS } from '@/constants/questionnaireQuestions';

export default function QuestionnaireIndex() {
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  // Redirect if user is not authenticated
  useEffect(() => {
    if (!user) {
      router.replace('/(auth)/login');
    }
  }, [user, router]);

  const handleStartQuestionnaire = () => {
    router.push('/(app)/onboarding/questionnaire/1');
  };

  const handleSkipToProfile = () => {
    router.push('/profiles/add');
  };

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="flex-1 justify-center items-center px-6">
        <Animated.View 
          entering={FadeIn.duration(600)}
          className="w-full max-w-sm"
        >
          {/* Header */}
          <Animated.View 
            entering={FadeInDown.duration(600).delay(200)}
            className="items-center mb-8"
          >
            <View className="p-4 mb-4 bg-primary/10 rounded-full">
              <Feather name="gift" size={48} color="#A3002B" />
            </View>
            <Text className="text-3xl font-bold text-center text-text-primary dark:text-text-primary-dark mb-3">
              Let's Create Magic! ✨
            </Text>
            <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark leading-6">
              Help us understand who you're shopping for so we can suggest the perfect gifts
            </Text>
          </Animated.View>

          {/* Benefits */}
          <Animated.View 
            entering={FadeInDown.duration(600).delay(400)}
            className="mb-8"
          >
            <View className="flex-row items-center mb-3">
              <Feather name="zap" size={20} color="#A3002B" />
              <Text className="ml-3 text-base text-text-primary dark:text-text-primary-dark">
                Get personalized gift ideas instantly
              </Text>
            </View>
            <View className="flex-row items-center mb-3">
              <Feather name="clock" size={20} color="#A3002B" />
              <Text className="ml-3 text-base text-text-primary dark:text-text-primary-dark">
                Takes just 2-3 minutes
              </Text>
            </View>
            <View className="flex-row items-center">
              <Feather name="heart" size={20} color="#A3002B" />
              <Text className="ml-3 text-base text-text-primary dark:text-text-primary-dark">
                Never miss their special moments
              </Text>
            </View>
          </Animated.View>

          {/* Action Buttons */}
          <Animated.View 
            entering={FadeInDown.duration(600).delay(600)}
            className="gap-4"
          >
            <Button
              title="Start Gift Journey"
              onPress={handleStartQuestionnaire}
              variant="primary"
              className="w-full"
              leftIcon={<Feather name="arrow-right" size={20} color="white" />}
            />
            
            <Button
              title="Skip for Now"
              onPress={handleSkipToProfile}
              variant="secondary"
              className="w-full"
            />
          </Animated.View>

          {/* Progress Indicator */}
          <Animated.View 
            entering={FadeInDown.duration(600).delay(800)}
            className="mt-8 items-center"
          >
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              Step 1 of {TOTAL_QUESTIONS} questions
            </Text>
          </Animated.View>
        </Animated.View>
      </View>
    </SafeAreaView>
  );
}
