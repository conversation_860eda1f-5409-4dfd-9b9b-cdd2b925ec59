import { QuestionAnswer } from '@/contexts/QuestionnaireContext';

// Profile form data interface (matching the existing structure)
export interface ProfileFormData {
  name: string;
  relationship: string;
  birthday: any;
  anniversary: any;
  interestsInput: string;
  dislikesInput: string;
  preferences: {
    favoriteColor: string;
    preferredStyle: string;
    favoriteBrands: string[];
    budgetMin?: number;
    budgetMax?: number;
  };
  clothingSize: string;
  shoeSize: string;
  wishlistItems: string[];
  pastGiftsGiven: string[];
  generalNotes: string[];
  customDates: any[];
}

// Budget range mapping
const BUDGET_RANGES = {
  'under-25': { min: 10, max: 25 },
  '25-50': { min: 25, max: 50 },
  '50-100': { min: 50, max: 100 },
  '100-200': { min: 100, max: 200 },
  '200-plus': { min: 200, max: 500 },
};

// Style preference mapping
const STYLE_MAPPING = {
  'minimalist': 'Minimalist',
  'bohemian': 'Bohemian',
  'classic': 'Classic',
  'modern': 'Modern',
  'rustic': 'Rustic',
  'glamorous': 'Glamorous',
};

// Color preference mapping
const COLOR_MAPPING = {
  'warm-reds': 'Red',
  'cool-blues': 'Blue',
  'earth-tones': 'Brown',
  'soft-pastels': 'Pink',
  'bold-brights': 'Purple',
  'neutral-grays': 'Gray',
  'forest-greens': 'Green',
  'sunset-orange': 'Orange',
  'deep-purples': 'Purple',
};

/**
 * Maps questionnaire answers to profile form data structure
 * @param answers - Record of questionnaire answers by question ID
 * @returns Partial profile form data that can be used to pre-fill the form
 */
export function mapQuestionnaireToProfileForm(answers: Record<number, QuestionAnswer>): Partial<ProfileFormData> {
  const mappedData: Partial<ProfileFormData> = {};

  // Question 1: Name
  if (answers[1]?.answer) {
    mappedData.name = answers[1].answer as string;
  }

  // Question 2: Relationship
  if (answers[2]?.answer) {
    mappedData.relationship = answers[2].answer as string;
  }

  // Question 3: Interests (multi-selection)
  if (answers[3]?.answer && Array.isArray(answers[3].answer)) {
    const interests = answers[3].answer as string[];
    mappedData.interestsInput = interests.join(', ');
  }

  // Question 4: Style preference
  if (answers[4]?.answer) {
    const styleKey = answers[4].answer as string;
    const mappedStyle = STYLE_MAPPING[styleKey as keyof typeof STYLE_MAPPING];
    if (mappedStyle) {
      mappedData.preferences = {
        ...mappedData.preferences,
        preferredStyle: mappedStyle,
        favoriteColor: '',
        favoriteBrands: [],
      };
    }
  }

  // Question 5: Color preference
  if (answers[5]?.answer) {
    const colorKey = answers[5].answer as string;
    const mappedColor = COLOR_MAPPING[colorKey as keyof typeof COLOR_MAPPING];
    if (mappedColor) {
      mappedData.preferences = {
        ...mappedData.preferences,
        favoriteColor: mappedColor,
        preferredStyle: mappedData.preferences?.preferredStyle || '',
        favoriteBrands: mappedData.preferences?.favoriteBrands || [],
      };
    }
  }

  // Question 6: Budget range
  if (answers[6]?.answer) {
    const budgetKey = answers[6].answer as string;
    const budgetRange = BUDGET_RANGES[budgetKey as keyof typeof BUDGET_RANGES];
    if (budgetRange) {
      mappedData.preferences = {
        ...mappedData.preferences,
        budgetMin: budgetRange.min,
        budgetMax: budgetRange.max,
        favoriteColor: mappedData.preferences?.favoriteColor || '',
        preferredStyle: mappedData.preferences?.preferredStyle || '',
        favoriteBrands: mappedData.preferences?.favoriteBrands || [],
      };
    }
  }

  // Question 8: Things to avoid (dislikes)
  if (answers[8]?.answer) {
    const avoidText = answers[8].answer as string;
    if (avoidText.trim()) {
      mappedData.dislikesInput = avoidText.trim();
    }
  }

  // Initialize other fields as empty to avoid undefined issues
  mappedData.birthday = null;
  mappedData.anniversary = null;
  mappedData.clothingSize = '';
  mappedData.shoeSize = '';
  mappedData.wishlistItems = [];
  mappedData.pastGiftsGiven = [];
  mappedData.generalNotes = [];
  mappedData.customDates = [];

  return mappedData;
}

/**
 * Generates a helpful note based on questionnaire answers
 * @param answers - Record of questionnaire answers by question ID
 * @returns A note string that can be added to general notes
 */
export function generateQuestionnaireNote(answers: Record<number, QuestionAnswer>): string {
  const name = answers[1]?.answer as string || 'This person';
  const giftType = answers[7]?.answer as string;
  const interests = answers[3]?.answer as string[] || [];
  
  let note = `Profile created from questionnaire. `;
  
  if (giftType) {
    const giftTypeMap = {
      'practical': 'prefers practical and useful gifts',
      'experiential': 'prefers experiences and memories over material items',
      'sentimental': 'appreciates sentimental and personal gifts',
      'fun': 'enjoys fun and surprising gifts'
    };
    
    const preference = giftTypeMap[giftType as keyof typeof giftTypeMap];
    if (preference) {
      note += `${name} ${preference}. `;
    }
  }
  
  if (interests.length > 0) {
    note += `Main interests: ${interests.join(', ')}.`;
  }
  
  return note;
}

/**
 * Checks if questionnaire data is available and valid for pre-filling
 * @param answers - Record of questionnaire answers by question ID
 * @returns Boolean indicating if data is sufficient for pre-filling
 */
export function hasValidQuestionnaireData(answers: Record<number, QuestionAnswer>): boolean {
  // At minimum, we need name and relationship
  return !!(answers[1]?.answer && answers[2]?.answer);
}
