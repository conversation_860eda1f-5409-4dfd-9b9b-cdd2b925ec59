import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity } from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { useQuestionnaire } from '@/contexts/QuestionnaireContext';
import Animated, { FadeInDown, FadeIn, SlideInRight, SlideOutLeft } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import Button from '@/components/ui/Button';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';
import { QUESTIONNAIRE_QUESTIONS, TOTAL_QUESTIONS, getQuestionById } from '@/constants/questionnaireQuestions';
import TextInputQuestion from '@/components/questionnaire/TextInputQuestion';
import SelectionQuestion from '@/components/questionnaire/SelectionQuestion';
import VisualGridQuestion from '@/components/questionnaire/VisualGridQuestion';

export default function QuestionnaireStep() {
  const router = useRouter();
  const { step } = useLocalSearchParams();
  const { user } = useAuth();
  const { saveAnswer, getAnswer, setCurrentStep } = useQuestionnaire();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [answer, setAnswer] = useState<string | string[]>('');
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);

  const currentStep = parseInt(step as string) || 1;
  const currentQuestion = getQuestionById(currentStep);
  const totalSteps = TOTAL_QUESTIONS;

  // Debug logging
  console.log('Current step:', currentStep, 'Question:', currentQuestion?.title);

  // Redirect if user is not authenticated
  useEffect(() => {
    if (!user) {
      router.replace('/(auth)/login');
    }
  }, [user, router]);

  // Redirect if invalid step
  useEffect(() => {
    if (!currentQuestion) {
      router.replace('/(app)/onboarding/questionnaire/');
    }
  }, [currentQuestion, router]);

  // Load saved answer for current step
  useEffect(() => {
    const savedAnswer = getAnswer(currentStep);
    if (savedAnswer) {
      if (currentQuestion?.type === 'multi-selection') {
        setSelectedOptions(Array.isArray(savedAnswer.answer) ? savedAnswer.answer : []);
      } else {
        setAnswer(savedAnswer.answer as string);
      }
    }
  }, [currentStep, currentQuestion, getAnswer]);

  const handleNext = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Save answer to context/storage
    const answerToSave = currentQuestion?.type === 'multi-selection' ? selectedOptions : answer;
    await saveAnswer(currentStep, answerToSave);

    // Update current step
    const nextStep = currentStep + 1;
    await setCurrentStep(nextStep);

    if (currentStep < totalSteps) {
      router.push(`/(app)/onboarding/questionnaire/${nextStep}`);
    } else {
      router.push('/(app)/onboarding/questionnaire/complete');
    }
  };

  const handleBack = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (currentStep > 1) {
      router.back();
    } else {
      router.push('/(app)/onboarding/questionnaire/');
    }
  };

  const handleSkip = () => {
    router.push('/profiles/add');
  };

  const isAnswered = () => {
    if (!currentQuestion) return false;

    // If question is not required, it's always considered answered
    if (!currentQuestion.required) return true;

    if (currentQuestion.type === 'multi-selection') {
      return selectedOptions.length > 0;
    }

    // For all other question types (text, selection, visual-grid)
    return typeof answer === 'string' && answer.trim().length > 0;
  };

  if (!user) {
    return null; // Will redirect in useEffect
  }

  if (!currentQuestion) {
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center px-6">
          <Text className="text-lg text-text-primary dark:text-text-primary-dark">
            Question not found
          </Text>
          <Button
            title="Go Back"
            onPress={() => router.replace('/(app)/onboarding/questionnaire/')}
            className="mt-4"
          />
        </View>
      </SafeAreaView>
    );
  }

  const progressPercentage = (currentStep / totalSteps) * 100;

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      {/* Header with Progress */}
      <View className="px-6 pt-4 pb-2">
        <View className="flex-row items-center justify-between mb-4">
          <TouchableOpacity onPress={handleBack} className="p-2">
            <Feather name="arrow-left" size={24} color="#A3002B" />
          </TouchableOpacity>
          
          <Text className="text-sm font-medium text-text-secondary dark:text-text-secondary-dark">
            {currentStep} of {totalSteps}
          </Text>
          
          <TouchableOpacity onPress={handleSkip} className="p-2">
            <Text className="text-sm font-medium text-primary">Skip</Text>
          </TouchableOpacity>
        </View>

        {/* Progress Bar */}
        <View className="h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <Animated.View 
            className="h-full bg-primary rounded-full"
            style={{ width: `${progressPercentage}%` }}
            entering={SlideInRight.duration(400)}
          />
        </View>
      </View>

      <ScrollView className="flex-1 px-6">
        {/* Render Question Component Based on Type */}
        <View className="mt-8">
          {currentQuestion.type === 'text' && (
            <TextInputQuestion
              title={currentQuestion.title}
              subtitle={currentQuestion.subtitle}
              value={answer as string}
              onChangeText={setAnswer}
              placeholder={currentQuestion.placeholder}
              required={currentQuestion.required}
              icon={currentQuestion.icon as any}
            />
          )}

          {(currentQuestion.type === 'selection' || currentQuestion.type === 'multi-selection') && (
            <SelectionQuestion
              title={currentQuestion.title}
              subtitle={currentQuestion.subtitle}
              options={currentQuestion.options as any}
              selectedValue={answer as string}
              onSelect={setAnswer}
              required={currentQuestion.required}
              multiSelect={currentQuestion.type === 'multi-selection'}
              selectedValues={selectedOptions}
              onMultiSelect={setSelectedOptions}
            />
          )}

          {currentQuestion.type === 'visual-grid' && currentQuestion.options && (
            <VisualGridQuestion
              title={currentQuestion.title}
              subtitle={currentQuestion.subtitle}
              options={currentQuestion.options as any}
              selectedValue={answer as string}
              onSelect={setAnswer}
              required={currentQuestion.required}
              type={currentQuestion.visualType}
            />
          )}
        </View>
      </ScrollView>

      {/* Bottom Action */}
      <View className="p-6 border-t border-border dark:border-border-dark">
        <Button
          title={currentStep < totalSteps ? "Continue" : "Complete"}
          onPress={handleNext}
          disabled={!isAnswered()}
          className="w-full"
          rightIcon={<Feather name="arrow-right" size={20} color="white" />}
        />
      </View>
    </SafeAreaView>
  );
}
