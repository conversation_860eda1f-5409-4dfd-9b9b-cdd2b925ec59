import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Storage key for questionnaire data
const QUESTIONNAIRE_STORAGE_KEY = 'questionnaire_data';
const QUESTIONNAIRE_PROGRESS_KEY = 'questionnaire_progress';

// Questionnaire data interface
export interface QuestionnaireData {
  name: string;
  relationship: string;
  primaryInterests: string[];
  keyDislikes: string[];
  budgetRange: { min: number; max: number };
  stylePreference: string;
  favoriteColor: string;
  giftType: 'practical' | 'experiential' | 'mixed';
  ageRange?: string;
  specialOccasions?: string[];
}

// Individual answer interface
export interface QuestionAnswer {
  questionId: number;
  answer: string | string[];
  timestamp: number;
}

// Context interface
interface QuestionnaireContextType {
  // Data state
  answers: Record<number, QuestionAnswer>;
  currentStep: number;
  isCompleted: boolean;
  isLoading: boolean;
  
  // Actions
  saveAnswer: (questionId: number, answer: string | string[]) => Promise<void>;
  getAnswer: (questionId: number) => QuestionAnswer | undefined;
  setCurrentStep: (step: number) => Promise<void>;
  completeQuestionnaire: () => Promise<QuestionnaireData>;
  resetQuestionnaire: () => Promise<void>;
  loadProgress: () => Promise<void>;
  
  // Computed data
  getQuestionnaireData: () => QuestionnaireData | null;
  getProgressPercentage: () => number;
}

const QuestionnaireContext = createContext<QuestionnaireContextType | undefined>(undefined);

// Total number of questions
const TOTAL_QUESTIONS = 8;

export const QuestionnaireProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [answers, setAnswers] = useState<Record<number, QuestionAnswer>>({});
  const [currentStep, setCurrentStepState] = useState<number>(1);
  const [isCompleted, setIsCompleted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Load saved progress on mount
  useEffect(() => {
    loadProgress();
  }, []);

  const loadProgress = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Load answers
      const savedAnswers = await AsyncStorage.getItem(QUESTIONNAIRE_STORAGE_KEY);
      if (savedAnswers) {
        setAnswers(JSON.parse(savedAnswers));
      }
      
      // Load current step
      const savedStep = await AsyncStorage.getItem(QUESTIONNAIRE_PROGRESS_KEY);
      if (savedStep) {
        setCurrentStepState(parseInt(savedStep, 10));
      }
      
    } catch (error) {
      console.error('Failed to load questionnaire progress:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveAnswer = async (questionId: number, answer: string | string[]): Promise<void> => {
    try {
      const newAnswer: QuestionAnswer = {
        questionId,
        answer,
        timestamp: Date.now(),
      };

      const updatedAnswers = {
        ...answers,
        [questionId]: newAnswer,
      };

      setAnswers(updatedAnswers);
      
      // Save to AsyncStorage
      await AsyncStorage.setItem(QUESTIONNAIRE_STORAGE_KEY, JSON.stringify(updatedAnswers));
      
    } catch (error) {
      console.error('Failed to save answer:', error);
    }
  };

  const getAnswer = (questionId: number): QuestionAnswer | undefined => {
    return answers[questionId];
  };

  const setCurrentStep = async (step: number): Promise<void> => {
    try {
      setCurrentStepState(step);
      await AsyncStorage.setItem(QUESTIONNAIRE_PROGRESS_KEY, step.toString());
    } catch (error) {
      console.error('Failed to save current step:', error);
    }
  };

  const getProgressPercentage = (): number => {
    const answeredQuestions = Object.keys(answers).length;
    return (answeredQuestions / TOTAL_QUESTIONS) * 100;
  };

  const getQuestionnaireData = (): QuestionnaireData | null => {
    try {
      // Map answers to structured data
      const nameAnswer = answers[1]?.answer as string;
      const relationshipAnswer = answers[2]?.answer as string;
      const interestsAnswer = answers[3]?.answer as string[];

      if (!nameAnswer || !relationshipAnswer || !interestsAnswer) {
        return null; // Not enough data
      }

      // Extract other answers with defaults
      const styleAnswer = answers[4]?.answer as string || 'casual';
      const colorAnswer = answers[5]?.answer as string || 'blue';
      const budgetAnswer = answers[6]?.answer as string || '25-50';
      const giftTypeAnswer = answers[7]?.answer as string || 'mixed';
      const dislikesAnswer = answers[8]?.answer as string || '';

      // Map budget range
      const budgetMapping: Record<string, { min: number; max: number }> = {
        'under-25': { min: 10, max: 25 },
        '25-50': { min: 25, max: 50 },
        '50-100': { min: 50, max: 100 },
        '100-200': { min: 100, max: 200 },
        '200-plus': { min: 200, max: 500 },
      };

      const budgetRange = budgetMapping[budgetAnswer] || { min: 25, max: 100 };

      return {
        name: nameAnswer,
        relationship: relationshipAnswer,
        primaryInterests: Array.isArray(interestsAnswer) ? interestsAnswer : [interestsAnswer],
        keyDislikes: dislikesAnswer ? [dislikesAnswer] : [],
        budgetRange,
        stylePreference: styleAnswer,
        favoriteColor: colorAnswer,
        giftType: giftTypeAnswer as 'practical' | 'experiential' | 'mixed',
      };
    } catch (error) {
      console.error('Failed to parse questionnaire data:', error);
      return null;
    }
  };

  const completeQuestionnaire = async (): Promise<QuestionnaireData> => {
    try {
      setIsCompleted(true);
      
      const questionnaireData = getQuestionnaireData();
      if (!questionnaireData) {
        throw new Error('Incomplete questionnaire data');
      }

      // Mark as completed in storage
      await AsyncStorage.setItem('questionnaire_completed', 'true');
      
      return questionnaireData;
    } catch (error) {
      console.error('Failed to complete questionnaire:', error);
      throw error;
    }
  };

  const resetQuestionnaire = async (): Promise<void> => {
    try {
      setAnswers({});
      setCurrentStepState(1);
      setIsCompleted(false);
      
      // Clear from storage
      await AsyncStorage.multiRemove([
        QUESTIONNAIRE_STORAGE_KEY,
        QUESTIONNAIRE_PROGRESS_KEY,
        'questionnaire_completed'
      ]);
      
    } catch (error) {
      console.error('Failed to reset questionnaire:', error);
    }
  };

  const value: QuestionnaireContextType = {
    // State
    answers,
    currentStep,
    isCompleted,
    isLoading,
    
    // Actions
    saveAnswer,
    getAnswer,
    setCurrentStep,
    completeQuestionnaire,
    resetQuestionnaire,
    loadProgress,
    
    // Computed
    getQuestionnaireData,
    getProgressPercentage,
  };

  return (
    <QuestionnaireContext.Provider value={value}>
      {children}
    </QuestionnaireContext.Provider>
  );
};

// Hook to use the questionnaire context
export const useQuestionnaire = (): QuestionnaireContextType => {
  const context = useContext(QuestionnaireContext);
  if (context === undefined) {
    throw new Error('useQuestionnaire must be used within a QuestionnaireProvider');
  }
  return context;
};
