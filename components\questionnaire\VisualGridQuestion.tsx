import React from 'react';
import { View, Text, TouchableOpacity, Image } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeInDown, ZoomIn } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

export interface VisualOption {
  label: string;
  value: string;
  color?: string;
  image?: any; // Image source
  icon?: keyof typeof Feather.glyphMap;
}

interface VisualGridQuestionProps {
  title: string;
  subtitle: string;
  options: VisualOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  required?: boolean;
  columns?: number;
  type?: 'color' | 'image' | 'icon';
}

export default function VisualGridQuestion({
  title,
  subtitle,
  options,
  selectedValue,
  onSelect,
  required = false,
  columns = 3,
  type = 'color'
}: VisualGridQuestionProps) {

  const handleOptionPress = async (value: string) => {
    try {
      console.log('Visual grid option pressed:', value);
      await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      onSelect(value);
    } catch (error) {
      console.error('Error in handleOptionPress:', error);
      // Still call onSelect even if haptics fail
      onSelect(value);
    }
  };

  const renderOption = (option: VisualOption, index: number) => {
    const isSelected = selectedValue === option.value;

    return (
      <View
        key={option.value}
        style={{ flex: 1, marginHorizontal: 4, marginBottom: 12 }}
      >
        <TouchableOpacity
          onPress={() => handleOptionPress(option.value)}
          style={{
            aspectRatio: 1,
            borderRadius: 12,
            borderWidth: 2,
            borderColor: isSelected ? '#A3002B' : '#E5E7EB',
            alignItems: 'center',
            justifyContent: 'center',
            shadowColor: isSelected ? '#000' : 'transparent',
            shadowOffset: isSelected ? { width: 0, height: 2 } : { width: 0, height: 0 },
            shadowOpacity: isSelected ? 0.1 : 0,
            shadowRadius: isSelected ? 4 : 0,
            elevation: isSelected ? 2 : 0,
          }}
          activeOpacity={0.8}
        >
          {/* Visual Content */}
          <View style={{
            width: '100%',
            height: '100%',
            borderRadius: 8,
            overflow: 'hidden',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            {type === 'color' && option.color && (
              <View
                style={{
                  width: '100%',
                  height: '100%',
                  borderRadius: 8,
                  backgroundColor: option.color
                }}
              />
            )}

            {type === 'image' && option.image && (
              <Image
                source={option.image}
                style={{ width: '100%', height: '100%', borderRadius: 8 }}
                resizeMode="cover"
              />
            )}

            {type === 'icon' && option.icon && (
              <View style={{
                width: '100%',
                height: '100%',
                borderRadius: 8,
                backgroundColor: '#F3F4F6',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Feather
                  name={option.icon}
                  size={32}
                  color={isSelected ? '#A3002B' : '#6B7280'}
                />
              </View>
            )}
          </View>

          {/* Selection Indicator */}
          {isSelected && (
            <Animated.View
              entering={ZoomIn.duration(200)}
              style={{
                position: 'absolute',
                top: -8,
                right: -8,
                width: 24,
                height: 24,
                backgroundColor: '#A3002B',
                borderRadius: 12,
                alignItems: 'center',
                justifyContent: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.25,
                shadowRadius: 4,
                elevation: 4,
              }}
            >
              <Feather name="check" size={14} color="white" />
            </Animated.View>
          )}
          </TouchableOpacity>

        {/* Label */}
        <Text style={{
          textAlign: 'center',
          fontSize: 14,
          marginTop: 8,
          fontWeight: '500',
          color: isSelected ? '#A3002B' : '#6B7280'
        }}>
          {option.label}
        </Text>
      </View>
    );
  };

  // Safety check for options
  if (!options || options.length === 0) {
    return (
      <View className="gap-6">
        <Text className="text-center text-text-secondary dark:text-text-secondary-dark">
          No options available
        </Text>
      </View>
    );
  }

  // Split options into rows
  const rows = [];
  for (let i = 0; i < options.length; i += columns) {
    rows.push(options.slice(i, i + columns));
  }

  return (
    <Animated.View 
      entering={FadeInDown.duration(600).delay(400)}
      className="gap-6"
    >
      {/* Question Header */}
      <View className="items-center mb-4">
        <View className="p-4 mb-4 bg-primary/10 rounded-full">
          <Feather name="grid" size={32} color="#A3002B" />
        </View>
        <Text className="text-3xl font-bold text-center text-text-primary dark:text-text-primary-dark mb-3">
          {title}
          {required && <Text className="text-primary"> *</Text>}
        </Text>
        <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark leading-6">
          {subtitle}
        </Text>
      </View>

      {/* Visual Grid */}
      <View style={{ paddingHorizontal: 16 }}>
        {rows.map((row, rowIndex) => (
          <View key={rowIndex} style={{ flexDirection: 'row', justifyContent: 'center' }}>
            {row.map((option, optionIndex) =>
              renderOption(option, rowIndex * columns + optionIndex)
            )}
            {/* Fill empty spaces in last row */}
            {row.length < columns &&
              Array.from({ length: columns - row.length }).map((_, emptyIndex) => (
                <View key={`empty-${emptyIndex}`} style={{ flex: 1, marginHorizontal: 4 }} />
              ))
            }
          </View>
        ))}
      </View>

      {/* Helper Text */}
      <View className="px-2">
        <View className="flex-row items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <Feather name="eye" size={16} color="#8B5CF6" />
          <Text className="ml-2 text-sm text-purple-600 dark:text-purple-400">
            Visual preferences help us suggest items they'll love
          </Text>
        </View>
      </View>
    </Animated.View>
  );
}
