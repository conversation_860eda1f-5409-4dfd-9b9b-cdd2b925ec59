import { SelectionOption } from '@/components/questionnaire/SelectionQuestion';
import { VisualOption } from '@/components/questionnaire/VisualGridQuestion';

export interface QuestionnaireQuestion {
  id: number;
  title: string;
  subtitle: string;
  type: 'text' | 'selection' | 'multi-selection' | 'visual-grid' | 'budget-range';
  required: boolean;
  options?: SelectionOption[] | VisualOption[];
  visualType?: 'color' | 'image' | 'icon';
  placeholder?: string;
  helperText?: string;
  icon?: string;
}

export const QUESTIONNAIRE_QUESTIONS: QuestionnaireQuestion[] = [
  {
    id: 1,
    title: "What's their name?",
    subtitle: "Let's make this personal - what should we call them?",
    type: 'text',
    required: true,
    placeholder: "Enter their name",
    helperText: "This helps us create a personalized experience",
    icon: "user"
  },
  {
    id: 2,
    title: "How are they related to you?",
    subtitle: "Understanding your relationship helps us suggest appropriate gifts",
    type: 'selection',
    required: true,
    options: [
      { 
        label: 'Romantic Partner', 
        value: 'partner', 
        icon: 'heart',
        description: 'Spouse, boyfriend, girlfriend, fiancé'
      },
      { 
        label: 'Family Member', 
        value: 'family', 
        icon: 'users',
        description: 'Parent, sibling, child, relative'
      },
      { 
        label: 'Close Friend', 
        value: 'friend', 
        icon: 'smile',
        description: 'Best friend, close companion'
      },
      { 
        label: 'Colleague', 
        value: 'colleague', 
        icon: 'briefcase',
        description: 'Coworker, business associate'
      },
    ]
  },
  {
    id: 3,
    title: "What do they love most?",
    subtitle: "Pick their biggest interests and hobbies",
    type: 'multi-selection',
    required: true,
    options: [
      { label: 'Technology & Gadgets', value: 'technology', icon: 'smartphone' },
      { label: 'Fashion & Style', value: 'fashion', icon: 'shopping-bag' },
      { label: 'Sports & Fitness', value: 'sports', icon: 'activity' },
      { label: 'Arts & Crafts', value: 'arts', icon: 'edit-3' },
      { label: 'Cooking & Food', value: 'cooking', icon: 'coffee' },
      { label: 'Travel & Adventure', value: 'travel', icon: 'map-pin' },
      { label: 'Books & Reading', value: 'reading', icon: 'book' },
      { label: 'Music & Entertainment', value: 'music', icon: 'music' },
      { label: 'Home & Garden', value: 'home', icon: 'home' },
      { label: 'Health & Wellness', value: 'wellness', icon: 'heart' },
    ]
  },
  {
    id: 4,
    title: "What's their style vibe?",
    subtitle: "Choose the aesthetic that best describes them",
    type: 'visual-grid',
    required: true,
    visualType: 'icon',
    options: [
      { label: 'Minimalist', value: 'minimalist', icon: 'minus' },
      { label: 'Bohemian', value: 'bohemian', icon: 'feather' },
      { label: 'Classic', value: 'classic', icon: 'award' },
      { label: 'Modern', value: 'modern', icon: 'zap' },
      { label: 'Rustic', value: 'rustic', icon: 'tree' },
      { label: 'Glamorous', value: 'glamorous', icon: 'star' },
    ]
  },
  {
    id: 5,
    title: "What's their favorite color palette?",
    subtitle: "Colors can reveal a lot about gift preferences",
    type: 'visual-grid',
    required: false,
    visualType: 'color',
    options: [
      { label: 'Warm Reds', value: 'warm-reds', color: '#DC2626' },
      { label: 'Cool Blues', value: 'cool-blues', color: '#2563EB' },
      { label: 'Earth Tones', value: 'earth-tones', color: '#92400E' },
      { label: 'Soft Pastels', value: 'soft-pastels', color: '#EC4899' },
      { label: 'Bold Brights', value: 'bold-brights', color: '#7C3AED' },
      { label: 'Neutral Grays', value: 'neutral-grays', color: '#6B7280' },
      { label: 'Forest Greens', value: 'forest-greens', color: '#059669' },
      { label: 'Sunset Orange', value: 'sunset-orange', color: '#EA580C' },
      { label: 'Deep Purples', value: 'deep-purples', color: '#7C2D12' },
    ]
  },
  {
    id: 6,
    title: "What's your gift budget range?",
    subtitle: "This helps us suggest gifts within your comfort zone",
    type: 'selection',
    required: true,
    options: [
      { label: 'Under $25', value: 'under-25', icon: 'dollar-sign' },
      { label: '$25 - $50', value: '25-50', icon: 'dollar-sign' },
      { label: '$50 - $100', value: '50-100', icon: 'dollar-sign' },
      { label: '$100 - $200', value: '100-200', icon: 'dollar-sign' },
      { label: '$200+', value: '200-plus', icon: 'dollar-sign' },
    ]
  },
  {
    id: 7,
    title: "What type of gifts do they prefer?",
    subtitle: "Understanding their gift personality helps us nail the perfect suggestion",
    type: 'selection',
    required: true,
    options: [
      { 
        label: 'Practical & Useful', 
        value: 'practical', 
        icon: 'tool',
        description: 'Things they can use in daily life'
      },
      { 
        label: 'Experiences & Memories', 
        value: 'experiential', 
        icon: 'camera',
        description: 'Activities, events, and adventures'
      },
      { 
        label: 'Sentimental & Personal', 
        value: 'sentimental', 
        icon: 'heart',
        description: 'Meaningful, customized items'
      },
      { 
        label: 'Fun & Surprising', 
        value: 'fun', 
        icon: 'gift',
        description: 'Unique, unexpected delights'
      },
    ]
  },
  {
    id: 8,
    title: "Any specific things to avoid?",
    subtitle: "Help us steer clear of gift disasters (optional)",
    type: 'text',
    required: false,
    placeholder: "e.g., allergic to nuts, doesn't like jewelry, prefers experiences over things...",
    helperText: "This is your safety net - mention anything that would be a definite no",
    icon: "shield"
  }
];

export const TOTAL_QUESTIONS = QUESTIONNAIRE_QUESTIONS.length;

// Helper function to get question by ID
export const getQuestionById = (id: number): QuestionnaireQuestion | undefined => {
  return QUESTIONNAIRE_QUESTIONS.find(q => q.id === id);
};

// Helper function to get next question ID
export const getNextQuestionId = (currentId: number): number | null => {
  const currentIndex = QUESTIONNAIRE_QUESTIONS.findIndex(q => q.id === currentId);
  if (currentIndex === -1 || currentIndex === QUESTIONNAIRE_QUESTIONS.length - 1) {
    return null;
  }
  return QUESTIONNAIRE_QUESTIONS[currentIndex + 1].id;
};

// Helper function to get previous question ID
export const getPreviousQuestionId = (currentId: number): number | null => {
  const currentIndex = QUESTIONNAIRE_QUESTIONS.findIndex(q => q.id === currentId);
  if (currentIndex <= 0) {
    return null;
  }
  return QUESTIONNAIRE_QUESTIONS[currentIndex - 1].id;
};
