import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Animated, { FadeInDown } from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';

export interface SelectionOption {
  label: string;
  value: string;
  icon: keyof typeof Feather.glyphMap;
  description?: string;
}

interface SelectionQuestionProps {
  title: string;
  subtitle: string;
  options: SelectionOption[];
  selectedValue: string;
  onSelect: (value: string) => void;
  required?: boolean;
  multiSelect?: boolean;
  selectedValues?: string[];
  onMultiSelect?: (values: string[]) => void;
}

export default function SelectionQuestion({
  title,
  subtitle,
  options,
  selectedValue,
  onSelect,
  required = false,
  multiSelect = false,
  selectedValues = [],
  onMultiSelect
}: SelectionQuestionProps) {

  const handleOptionPress = async (value: string) => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    
    if (multiSelect && onMultiSelect) {
      const newSelection = selectedValues.includes(value)
        ? selectedValues.filter(item => item !== value)
        : [...selectedValues, value];
      onMultiSelect(newSelection);
    } else {
      onSelect(value);
    }
  };

  const isSelected = (value: string) => {
    return multiSelect ? selectedValues.includes(value) : selectedValue === value;
  };

  return (
    <Animated.View 
      entering={FadeInDown.duration(600).delay(400)}
      className="gap-6"
    >
      {/* Question Header */}
      <View className="items-center mb-4">
        <View className="p-4 mb-4 bg-primary/10 rounded-full">
          <Feather name="list" size={32} color="#A3002B" />
        </View>
        <Text className="text-3xl font-bold text-center text-text-primary dark:text-text-primary-dark mb-3">
          {title}
          {required && <Text className="text-primary"> *</Text>}
        </Text>
        <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark leading-6">
          {subtitle}
        </Text>
      </View>

      {/* Selection Options */}
      <View className="gap-3">
        {options.map((option, index) => {
          const selected = isSelected(option.value);
          
          return (
            <Animated.View
              key={option.value}
              entering={FadeInDown.duration(400).delay(500 + index * 100)}
            >
              <TouchableOpacity
                onPress={() => handleOptionPress(option.value)}
                className={`p-4 rounded-xl border-2 flex-row items-center ${
                  selected 
                    ? 'border-primary bg-primary/10' 
                    : 'border-border dark:border-border-dark bg-card dark:bg-card-dark'
                }`}
                activeOpacity={0.7}
              >
                {/* Icon */}
                <View className={`p-3 rounded-lg mr-4 ${
                  selected ? 'bg-primary' : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  <Feather 
                    name={option.icon} 
                    size={24} 
                    color={selected ? 'white' : '#A3002B'} 
                  />
                </View>
                
                {/* Content */}
                <View className="flex-1">
                  <Text className={`text-lg font-semibold ${
                    selected 
                      ? 'text-primary' 
                      : 'text-text-primary dark:text-text-primary-dark'
                  }`}>
                    {option.label}
                  </Text>
                  {option.description && (
                    <Text className="text-sm text-text-secondary dark:text-text-secondary-dark mt-1">
                      {option.description}
                    </Text>
                  )}
                </View>

                {/* Selection Indicator */}
                {multiSelect && (
                  <View className={`w-6 h-6 rounded-full border-2 ml-3 items-center justify-center ${
                    selected 
                      ? 'border-primary bg-primary' 
                      : 'border-gray-300 dark:border-gray-600'
                  }`}>
                    {selected && (
                      <Feather name="check" size={14} color="white" />
                    )}
                  </View>
                )}
              </TouchableOpacity>
            </Animated.View>
          );
        })}
      </View>

      {/* Helper Text */}
      <View className="px-2">
        <View className="flex-row items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <Feather name="lightbulb" size={16} color="#10B981" />
          <Text className="ml-2 text-sm text-green-600 dark:text-green-400">
            {multiSelect 
              ? "You can select multiple options that apply" 
              : "Choose the option that best fits"
            }
          </Text>
        </View>
      </View>
    </Animated.View>
  );
}
