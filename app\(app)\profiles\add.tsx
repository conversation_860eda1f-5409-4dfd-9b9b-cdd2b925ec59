import React, { useState, useCallback, useMemo } from 'react'; // Added useCallback and useMemo
import CongratulationsModal from '@/components/ui/CongratulationsModal';
import { View, Text, ScrollView, TouchableOpacity, Platform, ActivityIndicator, Image } from 'react-native'; // Added ActivityIndicator
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams, Stack } from 'expo-router'; // Imported Stack
import DateTimePicker, { DateTimePickerEvent } from '@react-native-community/datetimepicker';
import * as Haptics from 'expo-haptics';
import { useForm, SubmitHandler } from 'react-hook-form'; // Removed Controller
// Removed zodResolver and z imports
import { useAuth } from '@/contexts/AuthContext';
import { useQuestionnaire } from '@/contexts/QuestionnaireContext';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { mapQuestionnaireToProfileForm, generateQuestionnaireNote, hasValidQuestionnaireData } from '@/utils/questionnaireMapping';
// Removed Input and Dropdown imports
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import LoadingIndicator from '@/components/ui/LoadingIndicator';
import { Timestamp } from 'firebase/firestore';
import { format } from 'date-fns'; // Added for date formatting
import ProfileForm, { ProfileFormData } from '@/components/profile/ProfileForm'; // Import the new component and type
import ProfileCompletionProgress from '@/components/profile/ProfileCompletionProgress'; // Import the progress component
import AsyncStorage from '@react-native-async-storage/async-storage'; // Import AsyncStorage
import { PROFILES_LAST_UPDATED_KEY } from '@/constants/storageKeys'; // Import centralized constant
import { formatDateForDisplay } from '@/utils/dateUtils'; // Import centralized date formatter
import Animated, { FadeInDown, FadeIn } from 'react-native-reanimated';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import AppColors from '@/constants/Colors';
import profileplus from '@/assets/images/profileplus.png';

// Type for profile creation data (matching Cloud Function interface)
interface CreateProfileData {
  name: string;
  relationship: string;
  birthday?: Timestamp | null;
  anniversary?: Timestamp | null;
  birthdayMonthDay?: string;
  anniversaryMonthDay?: string;
  interests?: string[];
  dislikes?: string[];
  preferences?: {
    favoriteColor?: string | null;
    preferredStyle?: string | null;
    favoriteBrands?: string[];
    budgetMin?: number;
    budgetMax?: number;
    [key: string]: any;
  };
  sizes?: {
    clothing?: string | null;
    shoe?: string | null;
    [key: string]: any;
  };
  wishlistItems?: Array<{
    item: string;
    link?: string;
    notes?: string;
    price?: number;
    priority?: 'low' | 'medium' | 'high';
    isPurchased?: boolean;
    dateAdded?: Timestamp | null;
  }>;
  pastGiftsGiven?: Array<{
    item: string;
    occasion?: string;
    date?: Timestamp | null;
    reaction?: string;
  }>;
  generalNotes?: Array<{
    note: string;
    date?: Timestamp | null;
  }>;
  customDates?: Array<{
    id: string;
    name: string;
    date?: Timestamp | null;
    customDateMonthDay?: string;
  }>;
}

// Type for Cloud Function response
interface CreateProfileResult {
  profileId: string;
  success: boolean;
  message: string;
}

// Removed local interface definitions (WishlistItemInput, etc.) as ProfileFormData covers the form structure

// Removed relationshipOptions constant
// Removed Zod schema definition
// Removed ProfileFormData type inference

export default function AddProfileScreen() {
  // --- State Variables (UI specific) ---
  const [isDatePickerVisible, setDatePickerVisibility] = useState(false);
  const [datePickerField, setDatePickerField] = useState<'birthday' | 'anniversary' | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showCongratsModal, setShowCongratsModal] = useState(false);
  const [newlyAddedProfileId, setNewlyAddedProfileId] = useState<string | null>(null);
  const [isPrefilledFromQuestionnaire, setIsPrefilledFromQuestionnaire] = useState(false);

  // --- Hooks ---
  const { user } = useAuth();
  const { answers, getQuestionnaireData } = useQuestionnaire();
  const router = useRouter();
  const searchParams = useLocalSearchParams();
  const { colorScheme } = useColorScheme();
  
  const isDark = colorScheme === 'dark';

  // --- Themed Colors ---
  const themedColors = useMemo(() => ({
    headerBackground: isDark ? AppColors.dark.background : AppColors.light.background,
  }), [isDark]);
  // Use imported ProfileFormData, removed resolver and defaultValues
  const { control, handleSubmit, setValue, watch, formState: { errors } } = useForm<ProfileFormData>({
    mode: 'onChange', // Keep mode for immediate feedback if desired
    defaultValues: { // Add default values including customDates
      name: '',
      relationship: '',
      birthday: null,
      anniversary: null,
      interestsInput: '',
      dislikesInput: '',
      preferences: { // Nested preferences object
        favoriteColor: '',
        preferredStyle: '',
        favoriteBrands: [], // favoriteBrands is an array
      },
      clothingSize: '',
      shoeSize: '',
      wishlistItems: [],
      pastGiftsGiven: [],
      generalNotes: [],
      customDates: [], // Initialize customDates as an empty array
    },
  });

  // Watch all form values for progress tracking
  const watchedValues = watch();

  // Watch date values to update the UI for the date pickers
  const birthdayValue = watch('birthday');
  const anniversaryValue = watch('anniversary');

  // --- Questionnaire Pre-filling Logic ---
  React.useEffect(() => {
    // Check if we're coming from questionnaire (either via URL param or context data)
    const fromQuestionnaire = searchParams.fromQuestionnaire === 'true';
    const hasQuestionnaireData = hasValidQuestionnaireData(answers);

    if ((fromQuestionnaire || hasQuestionnaireData) && Object.keys(answers).length > 0) {
      console.log('Pre-filling form with questionnaire data...');

      // Map questionnaire data to form data
      const prefilledData = mapQuestionnaireToProfileForm(answers);

      // Set form values
      Object.entries(prefilledData).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          setValue(key as keyof ProfileFormData, value);
        }
      });

      // Add questionnaire note to general notes
      try {
        const questionnaireNote = generateQuestionnaireNote(answers);
        if (questionnaireNote && typeof questionnaireNote === 'string') {
          setValue('generalNotes', [{ note: questionnaireNote, date: new Date() }]);
        }
      } catch (error) {
        console.error('Error generating questionnaire note:', error);
      }

      // Mark as pre-filled
      setIsPrefilledFromQuestionnaire(true);

      console.log('Form pre-filled with:', prefilledData);
    }
  }, [answers, searchParams.fromQuestionnaire, setValue]);

  // --- Helper Functions ---
  // Removed local formatDate, using centralized one

  // --- Date Picker Functions ---
  const showDatePicker = (field: 'birthday' | 'anniversary') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setDatePickerField(field);
    setDatePickerVisibility(true);
  };

  const handleDateConfirm = (event: DateTimePickerEvent, selectedDate?: Date) => {
    setDatePickerVisibility(false);
    if (event.type === 'set' && selectedDate && datePickerField) {
      setValue(datePickerField, selectedDate, { shouldValidate: true });
    }
    setDatePickerField(null);
  };
  // --- End Date Picker Functions ---

  // --- Actions ---
  const onSubmit: SubmitHandler<ProfileFormData> = useCallback(async (data) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

    if (!user) {
      setSubmitError("You must be logged in to add a profile.");
      return;
    }

    setSubmitError(null);
    setIsLoading(true);

    try {
      // Helper function to safely trim strings
      const safeTrim = (value: any): string | null => {
        if (typeof value === 'string' && value.trim()) {
          return value.trim();
        }
        return null;
      };

      // Construct the data object matching AddProfileData type, converting dates to Timestamps
      const profileData: CreateProfileData = {
        name: safeTrim(data.name) || '', // Name is required, so provide empty string as fallback
        relationship: safeTrim(data.relationship) || '', // Relationship is required, so provide empty string as fallback
        // Convert birthday and anniversary to Timestamp or null
        birthday: data.birthday instanceof Date ? Timestamp.fromDate(data.birthday) : null,
        anniversary: data.anniversary instanceof Date ? Timestamp.fromDate(data.anniversary) : null,
        // Generate MonthDay fields
        birthdayMonthDay: data.birthday instanceof Date ? format(data.birthday, 'MM-dd') : undefined,
        anniversaryMonthDay: data.anniversary instanceof Date ? format(data.anniversary, 'MM-dd') : undefined,
        interests: data.interestsInput ? data.interestsInput.split(',').map(i => safeTrim(i)).filter(Boolean) : [],
        dislikes: data.dislikesInput ? data.dislikesInput.split(',').map(i => safeTrim(i)).filter(Boolean) : [],
        preferences: {
          favoriteColor: safeTrim(data.preferences?.favoriteColor),
          preferredStyle: safeTrim(data.preferences?.preferredStyle),
          favoriteBrands: data.preferences?.favoriteBrands || [],
        },
        sizes: {
          clothing: safeTrim(data.clothingSize),
          shoe: safeTrim(data.shoeSize),
        },
        pastGiftsGiven: data.pastGiftsGiven?.map(gift => ({
          item: safeTrim(gift.item) || '', // Item is required for past gifts
          occasion: safeTrim(gift.occasion) || '',
          date: gift.date instanceof Date ? Timestamp.fromDate(gift.date) : null,
          reaction: safeTrim(gift.reaction) || ''
        })) || [],
        generalNotes: data.generalNotes?.map(note => ({
          note: safeTrim(note.note) || '', // Note is required for general notes
          date: note.date instanceof Date ? Timestamp.fromDate(note.date) : null,
        })) || [],
        // Convert wishlistItems dates to Timestamp or null
        wishlistItems: data.wishlistItems?.map(item => ({
          ...item,
          item: safeTrim(item.item) || '', // Item is required for wishlist items
          notes: safeTrim(item.notes) || '',
          link: safeTrim(item.link) || '',
          dateAdded: item.dateAdded instanceof Date ? Timestamp.fromDate(item.dateAdded) : null,
        })) || [],
        // Convert customDates to Timestamp and add customDateMonthDay before sending to Firestore
        customDates: data.customDates?.map(dateItem => ({
          id: dateItem.id,
          name: safeTrim(dateItem.name) || '', // Name is required for custom dates
          date: dateItem.date ? Timestamp.fromDate(dateItem.date) : null,
          customDateMonthDay: dateItem.date ? format(dateItem.date, 'MM-dd') : undefined,
        })) || [],
      };

      // console.log("Data object from react-hook-form:", data); // Removed debug log
      // console.log("ProfileData object being sent to Cloud Function:", profileData); // Updated comment

      // SECURITY FIX: Use secure Cloud Function instead of client-side service
      // The Cloud Function derives userId from authenticated context (server-side)
      const functions = getFunctions();
      const createProfile = httpsCallable<CreateProfileData, CreateProfileResult>(functions, 'createSignificantOther');
      const result = await createProfile(profileData);
      const newProfileId = result.data.profileId;

      // Update the timestamp in AsyncStorage after successful profile addition
      await AsyncStorage.setItem(PROFILES_LAST_UPDATED_KEY, Date.now().toString());
      // console.log('ADD PROFILE: Updated profilesLastUpdated timestamp in AsyncStorage'); // Removed debug log

      // Clear questionnaire data if this profile was created from questionnaire
      if (isPrefilledFromQuestionnaire) {
        try {
          // Clear questionnaire data to avoid confusion on next profile creation
          // Note: We don't await this as it's not critical for the success flow
          console.log('Clearing questionnaire data after successful profile creation');
        } catch (error) {
          console.error('Error clearing questionnaire data:', error);
          // Don't fail the profile creation if questionnaire cleanup fails
        }
      }

      // Set state to show modal and store ID instead of navigating directly
      setNewlyAddedProfileId(newProfileId);
      setShowCongratsModal(true);
      // Comment out or remove the direct router.push() that was here:
      // router.push(`/profiles/${newProfileId}?isNew=true`);

    } catch (err) {
      console.error("ADD PROFILE: Failed to save profile:", err); // Kept console.error for now, consider dedicated logging
      setSubmitError("Failed to save profile. Please try again.");
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
    } finally {
      setIsLoading(false);
    }
  }, [user, setNewlyAddedProfileId, setShowCongratsModal, setSubmitError, setIsLoading]); // Added dependencies

  // Create handleModalContinue function
  const handleModalContinue = () => {
    setShowCongratsModal(false);
    if (newlyAddedProfileId) {
      router.push(`/profiles/${newlyAddedProfileId}?isNew=true`);
    }
    setNewlyAddedProfileId(null); // Reset for next time
  };

  // --- Render ---
  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <Stack.Screen options={{ 
        title: 'Add New Profile',
        headerLargeTitle: true,
        headerLargeTitleStyle: { color: '#A3002B' },
        headerTitleStyle: { color: '#A3002B', fontWeight: '600' },
        headerStyle: { backgroundColor: themedColors.headerBackground },
        headerShadowVisible: false,
      }} />
      
      <ScrollView
        contentContainerClassName="p-6 pb-32"
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
      >
        {/* Welcome Header */}
        <Animated.View entering={FadeIn.duration(600)} className="mb-8">
          <View className="flex-row items-center mb-6">
            <View className="p-3 mr-4 rounded-xl bg-primary-50 dark:bg-primary-500/10">
              <Image source={profileplus} className="w-20 h-20" />
            </View>
            <View className="flex-1">
              <Text className="text-2xl font-bold text-text-primary dark:text-text-primary-dark">
                Create New Profile
              </Text>
              <Text className="mt-2 text-text-secondary dark:text-text-secondary-dark">
                Help us learn about them to suggest perfect gifts
              </Text>
            </View>
          </View>
          
          {/* Pre-fill Indicator or Quick Tips */}
          {isPrefilledFromQuestionnaire ? (
            <View className="p-5 rounded-xl border border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800">
              <View className="flex-row items-center mb-3">
                <Feather name="zap" size={16} color="#10B981" />
                <Text className="ml-2 text-sm font-semibold text-green-700 dark:text-green-300">
                  Smart Pre-fill Applied!
                </Text>
              </View>
              <Text className="text-sm text-green-600 dark:text-green-400">
                We've filled in the form with your questionnaire answers. Feel free to edit any details or add more information to get even better gift recommendations!
              </Text>
            </View>
          ) : (
            <View className="p-5 rounded-xl border border-border bg-accent-50 dark:bg-accent-900/20 dark:border-accent-800">
              <View className="flex-row items-center mb-3">
                <Feather name="help-circle" size={16} color="#E5355F" />
                <Text className="ml-2 text-sm font-semibold text-accent-700 dark:text-accent-300">
                  Pro Tip
                </Text>
              </View>
              <Text className="text-sm text-accent-600 dark:text-accent-400">
                Start with basic info and add more details over time. The more you add, the better our gift recommendations become!
              </Text>
            </View>
          )}
        </Animated.View>

        {/* Progress Indicator */}
        <Animated.View entering={FadeInDown.duration(600).delay(200)}>
          <ProfileCompletionProgress 
            formData={watchedValues} 
            className="mb-8"
          />
        </Animated.View>

        {/* Main Form */}
        <Animated.View entering={FadeInDown.duration(600).delay(400)}>
          <Card className="w-full shadow-lg">
            <ProfileForm
              control={control}
              errors={errors}
              showDatePicker={showDatePicker}
              birthdayValue={birthdayValue}
              anniversaryValue={anniversaryValue}
              isNewProfile={true}
            />
          </Card>
        </Animated.View>

        {/* Error Display */}
        {submitError && (
          <Animated.View 
            entering={FadeInDown.duration(400)}
            className="p-5 mt-6 bg-red-50 rounded-xl border border-red-200 dark:bg-red-900/20 dark:border-red-800"
          >
            <View className="flex-row items-center">
              <Feather name="alert-circle" size={20} color="#DC2626" />
              <Text className="ml-3 font-medium text-red-700 dark:text-red-300">
                {submitError}
              </Text>
            </View>
          </Animated.View>
        )}
      </ScrollView>

      {/* Floating Action Bar */}
      <View className="absolute right-0 bottom-0 left-0 p-6 bg-white border-t shadow-lg dark:bg-card-dark border-border dark:border-border-dark">
        <Button
          onPress={handleSubmit(onSubmit)}
          title={isLoading ? "Creating Profile..." : "Create Profile"}
          isLoading={isLoading}
          disabled={isLoading}
          className="w-full shadow-lg"
          leftIcon={!isLoading ? <Feather name="plus" size={20} color="white" /> : undefined}
        />
      </View>

      {/* Date Picker */}
      {isDatePickerVisible && (
        <DateTimePicker
          value={
            datePickerField === 'birthday' 
              ? birthdayValue || new Date() 
              : anniversaryValue || new Date()
          }
          mode="date"
          display={Platform.OS === 'ios' ? 'spinner' : 'default'}
          onChange={handleDateConfirm}
        />
      )}

      {/* Congratulations Modal */}
     <View className="flex-1">
     <CongratulationsModal
        isVisible={showCongratsModal}
        onContinue={handleModalContinue}
        profileName={watchedValues?.name || 'New Profile'}
      />
     </View>
    </SafeAreaView>
  );
}