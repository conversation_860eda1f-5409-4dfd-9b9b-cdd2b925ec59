import React from 'react';
import { View, Text } from 'react-native';
import { Feather } from '@expo/vector-icons';
import Input from '@/components/ui/Input';
import Animated, { FadeInDown } from 'react-native-reanimated';

interface TextInputQuestionProps {
  title: string;
  subtitle: string;
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  required?: boolean;
  icon?: keyof typeof Feather.glyphMap;
}

export default function TextInputQuestion({
  title,
  subtitle,
  value,
  onChangeText,
  placeholder = "Type your answer...",
  required = false,
  icon = "edit-3"
}: TextInputQuestionProps) {
  return (
    <Animated.View 
      entering={FadeInDown.duration(600).delay(400)}
      className="gap-6"
    >
      {/* Question Header */}
      <View className="items-center mb-4">
        <View className="p-4 mb-4 bg-primary/10 rounded-full">
          <Feather name={icon} size={32} color="#A3002B" />
        </View>
        <Text className="text-3xl font-bold text-center text-text-primary dark:text-text-primary-dark mb-3">
          {title}
          {required && <Text className="text-primary"> *</Text>}
        </Text>
        <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark leading-6">
          {subtitle}
        </Text>
      </View>

      {/* Input Field */}
      <View className="px-2">
        <Input
          placeholder={placeholder}
          value={value}
          onChangeText={onChangeText}
          className="text-lg p-4"
          leftIcon={<Feather name="type" size={20} color="#A3002B" />}
        />
      </View>

      {/* Helper Text */}
      <View className="px-2">
        <View className="flex-row items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <Feather name="info" size={16} color="#3B82F6" />
          <Text className="ml-2 text-sm text-blue-600 dark:text-blue-400">
            This helps us personalize your experience
          </Text>
        </View>
      </View>
    </Animated.View>
  );
}
