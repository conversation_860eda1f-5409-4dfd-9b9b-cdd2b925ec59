import React, { useState, useEffect } from 'react';
import 'react-native-get-random-values';
import { Stack, useRouter, SplashScreen } from "expo-router";
import { useFonts } from 'expo-font';
import { SafeAreaProvider } from "react-native-safe-area-context";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { AuthProvider, useAuth } from "../contexts/AuthContext";
import { QuestionnaireProvider } from "../contexts/QuestionnaireContext";
import useProfileData from "../hooks/useProfileData";

import "../global.css";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../constants/storageKeys';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

function RootLayoutNav() {
  const { user, isLoading: authLoading, error: authError } = useAuth();
  const { profiles, isLoading: profilesLoading } = useProfileData();
  const router = useRouter();
  const [onboardingLoading, setOnboardingLoading] = useState(true);
  const [hasOnboarded, setHasOnboarded] = useState(false);

  // Check onboarding status on mount and when user auth state changes
  // This fixes the bug where users were redirected to onboarding after login
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        const value = await AsyncStorage.getItem(STORAGE_KEYS.HAS_COMPLETED_ONBOARDING);
        setHasOnboarded(value !== null);
      } catch (e) {
        console.error('Failed to load onboarding status', e);
        setHasOnboarded(false); // Default to false on error
      } finally {
        setOnboardingLoading(false);
      }
    };

    checkOnboardingStatus();
  }, [user]); // Re-check when auth state changes to pick up onboarding flag set by AuthContext

  useEffect(() => {
    const isLoading = authLoading || onboardingLoading || (user && profilesLoading);

    console.log('Routing check:', {
      user: !!user,
      hasOnboarded,
      profilesCount: profiles.length,
      isLoading,
      authLoading,
      onboardingLoading,
      profilesLoading
    });

    if (isLoading) {
      return; // Don't navigate until everything is loaded
    }

    SplashScreen.hideAsync(); // Hide the splash screen now that we're ready

    // CRITICAL FIX: Don't redirect away from auth pages when there's an auth error
    // This prevents users from being redirected to onboarding when login fails
    if (authError && !user) {
      // User is not authenticated and there's an auth error
      // Stay on current page (likely login/signup) to show the error
      return;
    }

    if (!hasOnboarded) {
      router.replace('/(onboarding)/' as any);
    } else if (user) {
      // Check if user has profiles - if not, redirect to questionnaire
      // Only redirect if profiles have finished loading
      if (!profilesLoading) {
        if (profiles.length === 0) {
          router.replace('/(app)/onboarding/questionnaire/' as any);
        } else {
          router.replace('/(app)/home');
        }
      }
    } else {
      router.replace('/(auth)/login');
    }
  }, [user, authLoading, onboardingLoading, hasOnboarded, authError, router, profiles, profilesLoading]);
  
  // This component must return a navigator. The useEffect above will handle the redirection.
  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Screen name="(app)" options={{ headerShown: false }}/>
      <Stack.Screen name="(auth)" options={{ headerShown: false }}/>
      <Stack.Screen name="(onboarding)" options={{ headerShown: false }}/>
      <Stack.Screen name="index" options={{ headerShown: false }}/>
    </Stack>
  );
}

export default function RootLayout() {
  const [loaded, error] = useFonts({
    'SpaceMono': require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  useEffect(() => {
    if (error) throw error;
  }, [error]);

  if (!loaded) {
    return null;
  }
  
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
            <AuthProvider>
                <QuestionnaireProvider>
                    <RootLayoutNav />
                </QuestionnaireProvider>
            </AuthProvider>
        </SafeAreaProvider>
    </GestureHandlerRootView>
  );
}
